/**
 * 测试空段落功能的简单脚本
 */

import { markdownSerializer } from './markdown-seriailizer';
import { EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL } from './const';
import { EditorSchema } from '../extensions';

// 测试解析功能（在浏览器环境中测试）
function testParsingInfo() {
  console.log('=== 解析功能说明 ===');
  console.log('解析功能需要在浏览器环境中测试，因为需要 DOM API');
  console.log('在浏览器中，以下 markdown 内容:');
  console.log(`
这是第一段内容

${EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL}

这是第三段内容
  `.trim());
  console.log('\n应该被解析为包含空段落 <p></p> 的 HTML');
}

// 测试序列化功能说明
function testSerializationInfo() {
  console.log('\n=== 序列化功能说明 ===');
  console.log('序列化功能已实现，主要逻辑如下:');
  console.log('1. 检测空段落的函数 isEmptyParagraph():');
  console.log('   - 检查段落是否没有子节点');
  console.log('   - 检查段落是否只包含 HardBreak 节点');
  console.log('   - 检查段落是否只包含空的文本节点');
  console.log('');
  console.log('2. ExternalNodeSerializerMap 中的 Paragraph 处理器:');
  console.log('   - 如果是空段落，输出:', EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL);
  console.log('   - 如果不是空段落，使用默认的段落序列化器');
  console.log('');
  console.log('3. 需要在编辑器环境中测试完整功能');
}

// 运行测试
function runTests() {
  if (typeof window === 'undefined') {
    // Node.js 环境
    console.log('空段落功能测试');
    console.log('EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL:', EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL);

    testParsingInfo();
    testSerializationInfo();
  } else {
    // 浏览器环境
    console.log('请在 Node.js 环境中运行此测试');
  }
}

runTests();
