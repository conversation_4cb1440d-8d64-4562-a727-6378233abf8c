import markdownit from 'markdown-it';
import markdownitMark from 'markdown-it-mark';
import taskListPlugin from 'markdown-it-task-lists';

import { generateImageUniqueId, ImageStatus } from '../const/image';
import { EditorSchema } from '../extensions';
import type { ImageAttrs } from '../extensions/image';
import { extractElement } from './util';
import { EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL } from './const';

const NODE_TEXT_NODE = 3;

let parseHTMLImpl: ((content: string) => HTMLElement) | null = null;

if (typeof window !== 'undefined') {
  import('@tiptap/core').then(({ elementFromString }) => {
    parseHTMLImpl = (content: string) => elementFromString(content);
  });
} else {
  import('jsdom').then(({ JSDOM }) => {
    parseHTMLImpl = (content: string) => {
      const dom = new JSDOM(`
        <!doctype html>
        <html lang="en">
        <head></head>
        <body>${content}</body>
        </html>
      `);
      return dom.window.document.body;
    };
  });
}

const removeWhitespaces = (node: HTMLElement) => {
  const children = node.childNodes;

  for (let i = children.length - 1; i >= 0; i -= 1) {
    const child = children[i];
    if (!child) {
      continue;
    }

    if (child.nodeType === 3 && child.nodeValue && /^(\n\s\s|\n)$/.test(child.nodeValue)) {
      node.removeChild(child);
    } else if (child.nodeType === 1) {
      removeWhitespaces(child as HTMLElement);
    }
  }

  return node;
};

// 目前这个 class 只为了将 markdown 转换为 prosemirror 的 node，反向转化暂不实现
export class MarkdownParse {
  private mdIt: markdownit;

  constructor() {
    this.mdIt = this.initMarkdownIt();
  }

  private initMarkdownIt() {
    const mdIt = markdownit({
      html: true,
      breaks: true,
      linkify: true,
    });
    mdIt.use(markdownitMark);
    mdIt.use(taskListPlugin);
    mdIt.set({
      // 和 code 插件保持对齐
      langPrefix: '',
    });

    // 添加对 <u> 标签的支持
    mdIt.inline.ruler.before('emphasis', 'underline', (state, silent) => {
      const start = state.pos;
      const max = state.posMax;

      // 检查开始标签 <u>
      if (start + 2 >= max) return false;
      if (state.src.slice(start, start + 3) !== '<u>') return false;

      // 寻找结束标签 </u>
      const end = state.src.indexOf('</u>', start + 3);
      if (end === -1) return false;

      if (!silent) {
        const openToken = state.push('underline_open', 'u', 1);
        openToken.markup = '<u>';

        state.pos = start + 3;
        state.posMax = end;
        state.md.inline.tokenize(state);

        const closeToken = state.push('underline_close', 'u', -1);
        closeToken.markup = '</u>';
      }

      state.pos = end + 4;
      state.posMax = max;
      return true;
    });

    mdIt.renderer.rules.underline_open = () => '<u>';
    mdIt.renderer.rules.underline_close = () => '</u>';

    this.addRendererRules(mdIt);
    return mdIt;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private withoutNewLine(renderer: any) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (...args: any[]) => {
      const rendered = renderer(...args);
      if (rendered === '\n') {
        return rendered;
      }
      if (rendered[rendered.length - 1] === '\n') {
        return rendered.slice(0, -1);
      }
      return rendered;
    };
  }

  private addRendererRules(md: markdownit) {
    md.renderer.rules.hardbreak = this.withoutNewLine(md.renderer.rules.hardbreak);

    md.renderer.rules.softbreak = this.withoutNewLine(md.renderer.rules.softbreak);

    // 保存原始的 fence 渲染器
    const defaultFenceRenderer =
      md.renderer.rules.fence ||
      ((tokens, idx, options, env, self) => self.renderToken(tokens, idx, options));

    md.renderer.rules.fence = (tokens, idx, options, env, self) => {
      const token = tokens[idx];
      if (!token) return '';
      const info = token.info ? token.info.trim() : '';

      // 检查是否为 mermaid 代码块
      if (info === 'mermaid') {
        const content = token.content || '';
        return `<div data-type="mermaid" data-mermaid-container=""><pre data-mermaid-content=""><code>${content}</code></pre></div>`;
      }

      // 对于非 mermaid 代码块，手动生成 HTML 以确保语言类名被正确设置
      const language = info.split(/\s+/)[0]; // 取第一个词作为语言
      const content = token.content || '';

      if (language) {
        return `<pre class="${language}"><code class="${language}">${content}</code></pre>`;
      } else {
        return `<pre><code>${content}</code></pre>`;
      }
    };

    md.renderer.rules.code_block = this.withoutNewLine(md.renderer.rules.code_block);

    // Save the original link renderer
    const defaultLinkRenderer =
      md.renderer.rules.link_open ||
      ((tokens, idx, options, env, self) => self.renderToken(tokens, idx, options));

    // Use the default renderer for non-YouTube links
    md.renderer.rules.link_open = (tokens, idx, options, env, self) => {
      return defaultLinkRenderer(tokens, idx, options, env, self);
    };

    const defaultLinkCloseRenderer =
      md.renderer.rules.link_close ||
      ((tokens, idx, options, env, self) => self.renderToken(tokens, idx, options));

    md.renderer.rules.link_close = (tokens, idx, options, env, self) => {
      const token = tokens[idx];
      if (token?.hidden) {
        return '';
      }
      return defaultLinkCloseRenderer(tokens, idx, options, env, self);
    };

    const defaultTextRenderer =
      md.renderer.rules.text ||
      ((tokens, idx, options, env, self) => self.renderToken(tokens, idx, options));

    md.renderer.rules.text = (tokens, idx, options, env, self) => {
      const token = tokens[idx];
      if (token?.hidden) {
        return '';
      }
      return defaultTextRenderer(tokens, idx, options, env, self);
    };

    // 添加自定义图片渲染规则
    md.renderer.rules.image = (tokens, idx) => {
      const token = tokens[idx];
      if (!token) return '';
      try {
        const alt = token.content || token.attrs?.find(([key]) => key === 'alt')?.[1] || '';
        const src = token.attrs?.find(([key]) => key === 'src')?.[1] || '';

        // 检查是否为 SVG 文件
        if (src.toLowerCase().endsWith('.svg')) {
          // 生成 SVG 节点的 HTML 结构
          return `<div data-type="SvgEditor" data-src="${src}" data-alt="${alt}"></div>`;
        }

        // 检查是否为 MP3 文件
        if (src.toLowerCase().endsWith('.mp3')) {
          // 生成 Audio 节点的 HTML 结构
          return `<div data-type="Audio" data-src="${src}" data-alt="${alt}"></div>`;
        }

        // 尝试解析 alt 中的 JSON
        let attrs: ImageAttrs = {};
        try {
          attrs = JSON.parse(alt);
        } catch {
          attrs = { alt, status: ImageStatus.LOADING };
        }

        // 构建 HTML 属性字符串
        const htmlAttrs = {
          src,
          id: attrs.id || generateImageUniqueId(),
          status: attrs.status,
          width: attrs.width,
          height: attrs.height,
          alt: attrs.alt || alt,
          text: attrs.text || '',
        };

        // 生成 HTML
        const attrString = Object.entries(htmlAttrs)
          .map(([key, value]) => `${key}="${value}"`)
          .join(' ');

        return `<img ${attrString}>`;
      } catch (error) {
        console.error('Error parsing image:', error);
        return token.markup || '';
      }
    };

    md.renderer.renderToken = this.withoutNewLine(md.renderer.renderToken.bind(md.renderer));

    return md;
  }

  private normalizeBlocks(node: HTMLElement) {
    const blocks = Object.values(EditorSchema.nodes).filter((node) => node.isBlock);

    const selector = blocks
      .flatMap((block) => block.spec.parseDOM?.map((spec) => spec.tag))
      .filter(Boolean)
      .join(',');

    if (!selector) {
      return;
    }

    [...node.querySelectorAll<HTMLElement>(selector)].forEach((el) => {
      if (el.parentElement?.matches('p')) {
        extractElement(el);
      }
    });
  }

  private normalizeDOM(node: HTMLElement) {
    this.normalizeBlocks(node);

    // 处理 codeblock
    node.innerHTML = node.innerHTML.replace(/\n<\/code><\/pre>/g, '</code></pre>');

    // 处理 tasklist
    node.querySelectorAll('.contains-task-list').forEach((el) => {
      el.setAttribute('data-type', 'taskList');
    });
    node.querySelectorAll('.task-list-item').forEach((item) => {
      const input = item.querySelector('input');
      item.setAttribute('data-type', 'taskItem');
      if (input) {
        item.setAttribute('data-checked', input.checked.toString());
        input.remove();
      }
    });

    // remove all \n appended by markdown-it
    node.querySelectorAll('*').forEach((el) => {
      if (el.nextSibling?.nodeType === NODE_TEXT_NODE && !el.closest('pre')) {
        el.nextSibling.textContent = el.nextSibling.textContent?.replace(/^\n/, '') ?? '';
      }
    });

    return node;
  }

  protected parseHTML(content: string): HTMLElement {
    if (!parseHTMLImpl) {
      throw new Error(
        'HTML parser not yet initialized. This usually happens when trying to use the parser immediately after import.',
      );
    }
    return parseHTMLImpl(content);
  }

  parse(content: string): string {
    // 将字面的 \n 转换为真正的换行符，这样 markdown-it 的 breaks 功能就能将其转换为 <br>
    let processedContent = content.replace(/\\n/g, '\n');

    // 处理空段落标记，将其转换为空的 <p> 标签
    processedContent = processedContent.replace(
      new RegExp(EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
      '<p></p>'
    );

    const renderHTML = this.mdIt.render(processedContent);
    const bodyElement = this.parseHTML(renderHTML);
    const element = removeWhitespaces(bodyElement);
    const normalizedElement = this.normalizeDOM(element);
    return normalizedElement.innerHTML;
  }
}

export const markdownParse = new MarkdownParse();
