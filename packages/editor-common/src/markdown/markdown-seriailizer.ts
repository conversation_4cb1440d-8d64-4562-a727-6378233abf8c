import { HardBreak } from '@tiptap/extension-hard-break';
import type { Node } from '@tiptap/pm/model';
import { MarkdownSerializerState } from './markdown-seriailizer-state';
import { MarkSerializerMap, TitleMarkSerializerMap } from './seriailizer-map/mark';
import {
  ExternalNodeSerializerMap,
  MarkEmptyParagraphNodeSerializerMap,
  NodeSerializerMap,
  TitleNodeSerializerMap,
} from './seriailizer-map/node';

// 这个 class 的作用是将编辑器里的内容转化为 markdown 格式内容
export class MarkdownSerializer {
  private state: MarkdownSerializerState;
  private externalState: MarkdownSerializerState;
  private titleState: MarkdownSerializerState;

  constructor() {
    this.state = new MarkdownSerializerState(this.nodes, this.marks, {
      hardBreakNodeName: HardBreak.name,
    });
    this.externalState = new MarkdownSerializerState(this.externalNodes, this.marks, {
      hardBreakNodeName: HardBreak.name,
    });
    this.titleState = new MarkdownSerializerState(this.titleNodes, this.titleMarks, {
      hardBreakNodeName: HardBreak.name,
    });
  }

  externalCustomSerialize(node: Node) {
    this.externalState.clearOutValue();
    this.externalState.renderContent(node);
    return this.externalState.outValue;
  }

  serialize(doc: Node) {
    this.state.clearOutValue();
    this.state.renderContent(doc);
    return this.state.outValue;
  }

  titleSerialize(doc: Node) {
    this.titleState.clearOutValue();
    this.titleState.renderContent(doc);
    return this.titleState.outValue;
  }

  get externalNodes() {
    return ExternalNodeSerializerMap;
  }

  get nodes() {
    return NodeSerializerMap;
  }

  get marks() {
    return MarkSerializerMap;
  }

  get titleNodes() {
    return TitleNodeSerializerMap;
  }

  get titleMarks() {
    return TitleMarkSerializerMap;
  }
}

export const markdownSerializer = new MarkdownSerializer();
