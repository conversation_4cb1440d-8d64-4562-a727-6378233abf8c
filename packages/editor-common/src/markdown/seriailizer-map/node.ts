import type { Node } from '@tiptap/pm/model';
import { defaultMarkdownSerializer } from 'prosemirror-markdown';
import { ImageStatus } from '../../const';
import { EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL } from '../const';
import {
  Audio,
  Blockquote,
  BulletList,
  CodeBlock,
  DiffBlock,
  Divider,
  Document,
  HardBreak,
  Heading,
  Image,
  ListItem,
  Mermaid,
  OrderedList,
  Paragraph,
  SelectionEnd,
  SelectionStart,
  SVG,
  Table,
  TaskItem,
  TaskList,
  Text,
  Youtube,
} from '../../extensions';
import type { IMarkdownSerializerState } from '../markdown-seriailizer-state';
import { findIndexOfAdjacentNode, htmlSerializer, isMarkdownSerializable } from './util';

export const BaseNodeSerializerMap: Record<
  string,
  (state: IMarkdownSerializerState, node: Node, parent: Node, index: number) => void
> = {
  [Document.name]: (state: IMarkdownSerializerState, node: Node, parent: Node, index: number) => {
    const serializer = defaultMarkdownSerializer.nodes.document;
    if (serializer) {
      serializer(state, node, parent, index);
    }
  },
  [Blockquote.name]: (state: IMarkdownSerializerState, node: Node, parent: Node, index: number) => {
    const serializer = defaultMarkdownSerializer.nodes.blockquote;
    if (serializer) {
      serializer(state, node, parent, index);
    }
  },
  [BulletList.name]: (state: IMarkdownSerializerState, node: Node, parent: Node, index: number) => {
    return state.renderList(node, '  ', () => '-' + ' ');
  },
  [CodeBlock.name]: (state: IMarkdownSerializerState, node: Node, parent: Node, index: number) => {
    state.write('```' + (node.attrs.language || '') + '\n');
    state.renderContent(node);
    state.ensureNewLine();
    state.write('```');
    state.closeBlock(node);
  },
  [Mermaid.name]: (state: IMarkdownSerializerState, node: Node, parent: Node, index: number) => {
    state.write('```mermaid\n');
    state.renderContent(node);
    state.ensureNewLine();
    state.write('```');
    state.closeBlock(node);
  },
  [HardBreak.name]: (state: IMarkdownSerializerState, node: Node, parent: Node, index: number) => {
    for (let i = index + 1; i < parent.childCount; i++)
      if (parent.child(i).type !== node.type) {
        state.write(state.inTable ? htmlSerializer(state, node, parent) : '\\\n');
        return;
      }
  },
  [Heading.name]: (state: IMarkdownSerializerState, node: Node, parent: Node, index: number) => {
    const serializer = defaultMarkdownSerializer.nodes.heading;
    if (serializer) {
      serializer(state, node, parent, index);
    }
  },
  [Divider.name]: (state: IMarkdownSerializerState, node: Node, parent: Node, index: number) => {
    const serializer = defaultMarkdownSerializer.nodes.horizontal_rule;
    if (serializer) {
      serializer(state, node, parent, index);
    }
  },
  [ListItem.name]: (state: IMarkdownSerializerState, node: Node, parent: Node, index: number) => {
    const serializer = defaultMarkdownSerializer.nodes.list_item;
    if (serializer) {
      serializer(state, node, parent, index);
    }
  },
  [OrderedList.name]: (
    state: IMarkdownSerializerState,
    node: Node,
    parent: Node,
    index: number,
  ) => {
    const start = node.attrs.start || 1;
    const maxW = String(start + node.childCount - 1).length;
    const space = state.repeat(' ', maxW + 2);
    const adjacentIndex = findIndexOfAdjacentNode(node, parent, index);
    const separator = adjacentIndex % 2 ? ') ' : '. ';
    state.renderList(node, space, (i) => {
      const nStr = String(start + i);
      return state.repeat(' ', maxW - nStr.length) + nStr + separator;
    });
  },
  [Paragraph.name]: (state: IMarkdownSerializerState, node: Node, parent: Node, index: number) => {
    const serializer = defaultMarkdownSerializer.nodes.paragraph;
    if (serializer) {
      serializer(state, node, parent, index);
    }
  },
  [Table.name]: (state: IMarkdownSerializerState, node: Node, parent: Node, index: number) => {
    if (!isMarkdownSerializable(node)) {
      htmlSerializer(state, node, parent);
      return;
    }
    state.inTable = true;

    node.forEach((row, p, i) => {
      state.write('| ');
      row.forEach((col, p, j) => {
        if (j) {
          state.write(' | ');
        }

        // 处理单元格内容，现在每个单元格只包含一个段落
        let cellText = '';
        const cellContent = col.firstChild;
        if (cellContent) {
          // 暂存当前输出，渲染单元格内容，然后获取结果
          const savedOut = state.outValue;
          state.clearOutValue();
          state.renderInline(cellContent);
          cellText = state.outValue.trim();
          state.clearOutValue();
          state.write(savedOut);
        }

        if (cellText) {
          // 转义特殊字符避免破坏表格格式
          const escapedText = cellText.replace(/\|/g, '\\|').replace(/\n/g, ' ');
          state.write(escapedText);
        }
      });
      state.write(' |');
      state.ensureNewLine();

      // 添加表头分隔符：如果有表头且是第一行，或者没有表头且是第一行
      if (i === 0) {
        const delimiterRow = Array.from({ length: row.childCount })
          .map(() => '---')
          .join(' | ');
        state.write(`| ${delimiterRow} |`);
        state.ensureNewLine();
      }
    });
    state.closeBlock(node);
    state.inTable = false;
  },
  [TaskItem.name]: (state: IMarkdownSerializerState, node: Node) => {
    const check = node.attrs.checked ? '[x]' : '[ ]';
    state.write(`${check} `);
    state.renderContent(node);
  },
  [TaskList.name]: (state: IMarkdownSerializerState, node: Node) => {
    return state.renderList(node, '  ', () => '-' + ' ');
  },
  [Text.name]: (state: IMarkdownSerializerState, node: Node) => {
    state.write(node.textContent);
  },
  [Youtube.name]: (state: IMarkdownSerializerState, node: Node) => {
    state.write(`[${node.attrs.src}](${node.attrs.src})`);
    state.closeBlock(node);
  },
  [SVG.name]: (state: IMarkdownSerializerState, node: Node) => {
    state.write(`![${node.attrs.alt}](${node.attrs.src})`);
    state.closeBlock(node);
  },
  [SelectionStart.name]: (state: IMarkdownSerializerState) => {
    state.write('<selection-start></selection-start>');
  },
  [SelectionEnd.name]: (state: IMarkdownSerializerState) => {
    state.write('<selection-end></selection-end>');
  },
  [Audio.name]: (state: IMarkdownSerializerState, node: Node) => {
    const attrs = {
      title: node.attrs.title || '',
      album: node.attrs.album || '',
    };
    state.write(`![${JSON.stringify(attrs)}](${node.attrs.src})`);
    state.closeBlock(node);
  },
  [DiffBlock.name]: (state: IMarkdownSerializerState, node: Node) => {
    // 开始 diff 块
    state.write(':::diff');
    if (node.attrs.diffBlockId) {
      state.write(` {id="${node.attrs.diffBlockId}"}`);
    }
    state.ensureNewLine();

    // 渲染 diff 块内容
    state.renderContent(node);

    // 结束 diff 块
    state.ensureNewLine();
    state.write(':::');
    state.closeBlock(node);
  },
};

export const ExternalNodeSerializerMap = {
  ...BaseNodeSerializerMap,
  [Image.name]: (state: IMarkdownSerializerState, node: Node, parent: Node, index: number) => {
    const serializer = defaultMarkdownSerializer.nodes.image;
    if (serializer) {
      serializer(state, node, parent, index);
    }
  },
};

export const NodeSerializerMap = {
  ...BaseNodeSerializerMap,
  // 自定义 image 序列化
  [Image.name]: (state: IMarkdownSerializerState, node: Node) => {
    const attrs = {
      alt: node.attrs.alt || '',
      id: node.attrs.id || '',
      text: node.attrs.text || '',
      width: node.attrs.width || undefined,
      height: node.attrs.height || undefined,
      status: node.attrs.src ? node.attrs.status || ImageStatus.LOADING : ImageStatus.FAILED,
    };

    state.write(`![${JSON.stringify(attrs)}](${node.attrs.src})`);
    state.closeBlock(node);
  },
};

export const TitleNodeSerializerMap = {
  ...NodeSerializerMap,
  // 在标题中不渲染图片
  [Image.name]: (state: IMarkdownSerializerState, node: Node, parent: Node, index: number) => {
    // 不做任何操作，完全忽略图片节点
  },
};

export const MarkEmptyParagraphNodeSerializerMap = {
  ...ExternalNodeSerializerMap,
  [Paragraph.name]: (state: IMarkdownSerializerState, node: Node, parent: Node, index: number) => {
    // 检查是否为空段落
    const isEmpty =
      node.childCount === 0 || // 完全没有子节点
      (node.childCount === 1 && node.firstChild?.type.name === HardBreak.name) || // 只有一个换行符
      node.textContent.trim() === ''; // 只有空白字符

    if (isEmpty) {
      // 空段落转换为特定字符串
      state.write(EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL);
      state.closeBlock(node);
    } else {
      // 非空段落使用默认序列化器
      const serializer = defaultMarkdownSerializer.nodes.paragraph;
      if (serializer) {
        serializer(state, node, parent, index);
      }
    }
  },
}
